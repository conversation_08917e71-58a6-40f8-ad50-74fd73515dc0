// Browser compatibility
const browserAPI = typeof browser !== 'undefined' ? browser : chrome;

// YouTube FYP Blocker Content Script
(function() {
  // Check if the feature is enabled from storage
  let youtubeBlockerEnabled = false;
  
  // Function to apply or remove the blocking class
  function toggleYouTubeBlocking(enabled) {
    if (enabled) {
      document.documentElement.classList.add('ytd-fyp-blocker-enabled');
      // Also handle any search results that might be lingering
      hideSearchResultsOnWatchPages();
    } else {
      document.documentElement.classList.remove('ytd-fyp-blocker-enabled');
    }
  }
  
  // Function to specifically hide search results that appear on watch pages
  function hideSearchResultsOnWatchPages() {
    // Check if we're on a watch page
    if (window.location.pathname.includes('/watch')) {
      // Hide any search result containers that might be visible
      const searchSelectors = [
        'ytd-search-pyv-renderer',
        '.search-pyvs',
        'ytd-search-header-renderer',
        'ytd-search-sub-menu-renderer'
      ];
      
      searchSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          element.style.display = 'none';
        });
      });
    }
  }
  
  // Function to handle YouTube navigation changes
  function handleNavigation() {
    if (youtubeBlockerEnabled) {
      setTimeout(() => {
        hideSearchResultsOnWatchPages();
      }, 100); // Small delay to ensure DOM is updated
    }
  }
  
  // Initialize from storage
  browserAPI.storage.sync.get(['youtubeBlockerEnabled'], function(result) {
    youtubeBlockerEnabled = result.youtubeBlockerEnabled === true;
    toggleYouTubeBlocking(youtubeBlockerEnabled);
  });
  
  // Listen for changes to the setting
  browserAPI.storage.onChanged.addListener(function(changes, namespace) {
    if (changes.youtubeBlockerEnabled) {
      youtubeBlockerEnabled = changes.youtubeBlockerEnabled.newValue;
      toggleYouTubeBlocking(youtubeBlockerEnabled);
    }
  });
  
  // Listen for YouTube navigation events
  window.addEventListener('yt-navigate-finish', handleNavigation);
  window.addEventListener('yt-page-data-updated', handleNavigation);
  
  // Also listen for URL changes using MutationObserver on the URL
  let currentUrl = window.location.href;
  const urlObserver = new MutationObserver(() => {
    if (window.location.href !== currentUrl) {
      currentUrl = window.location.href;
      handleNavigation();
    }
  });
  
  // Start observing
  urlObserver.observe(document.body, {
    childList: true,
    subtree: true
  });
})();
