// Browser compatibility
const browserAPI = typeof browser !== 'undefined' ? browser : chrome;

// Configuration
let settings = {
  newTabBlockerEnabled: false, // Default to false
  youtubeBlockerEnabled: false // Default to false
};

// Load settings from storage
browserAPI.storage.sync.get(['newTabBlockerEnabled', 'youtubeBlockerEnabled'], (result) => {
  // Only update if values exist in storage, otherwise keep defaults (false)
  if (result.newTabBlockerEnabled !== undefined) {
    settings.newTabBlockerEnabled = result.newTabBlockerEnabled;
  }
  if (result.youtubeBlockerEnabled !== undefined) {
    settings.youtubeBlockerEnabled = result.youtubeBlockerEnabled;
  }
  // Apply settings only after loading them
  applySettings(); 
});

// Listen for setting changes from storage
browserAPI.storage.onChanged.addListener(function(changes, namespace) {
  let settingsChanged = false;
  if (changes.newTabBlockerEnabled) {
    settings.newTabBlockerEnabled = changes.newTabBlockerEnabled.newValue;
    settingsChanged = true;
  }
  if (changes.youtubeBlockerEnabled) {
    settings.youtubeBlockerEnabled = changes.youtubeBlockerEnabled.newValue;
    settingsChanged = true;
  }
  if (settingsChanged) {
    applySettings();
  }
});

// Function to apply current settings
function applySettings() {
  if (settings.youtubeBlockerEnabled) {
    document.documentElement.classList.add('ytd-fyp-blocker-enabled');
    hideRecommendations(); // This will also call hideSearchResultsOnWatchPages if conditions are met
  } else {
    document.documentElement.classList.remove('ytd-fyp-blocker-enabled');
    showRecommendations(); // This will also call showSearchResultsOnWatchPages
    clearLegacyInlineStyles(); // Clean up any old inline styles from previous versions/logic
  }
  // New tab blocker logic is self-contained with its settings check
}

// Function to hide recommendations
function hideRecommendations() {
  if (!settings.youtubeBlockerEnabled) return; // Guard execution

  const selectors = [
    'ytd-rich-section-renderer',
    'ytd-reel-shelf-renderer',
    'ytd-horizontal-card-list-renderer',
    'ytd-shelf-renderer'
  ];

  selectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
      // Ensure we are targeting elements within the main content area for recommendations
      if (element.closest('ytd-two-column-browse-results-renderer') || element.closest('ytd-browse[page-subtype="home"]')) {
        element.style.setProperty('display', 'none', 'important');
      }
    });
  });
  hideSearchResultsOnWatchPages();
}

// Function to show recommendations
function showRecommendations() {
  const selectors = [
    'ytd-rich-section-renderer',
    'ytd-reel-shelf-renderer',
    'ytd-horizontal-card-list-renderer',
    'ytd-shelf-renderer'
  ];

  selectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
      if (element.closest('ytd-two-column-browse-results-renderer') || element.closest('ytd-browse[page-subtype="home"]')) {
        element.style.display = ''; // Revert display
      }
    });
  });
  showSearchResultsOnWatchPages();
}

// Function to specifically hide search results that appear on watch pages
function hideSearchResultsOnWatchPages() {
  if (!settings.youtubeBlockerEnabled) return; // Guard execution

  if (window.location.pathname.includes('/watch')) {
    const searchSelectors = [
      // These selectors were identified as potentially needing JS intervention
      // if not fully covered by youtube-blocker.css for watch pages.
      // Ideally, these would also be in youtube-blocker.css.
      'ytd-item-section-renderer[has-header]', // e.g., "Results from channel"
      'ytd-search-header-renderer',            // Header for search results section
      'ytd-search-sub-menu-renderer'           // Sub-menu for search filters
    ];
    searchSelectors.forEach(selector => {
      document.querySelectorAll(selector).forEach(element => {
        // Apply only if these elements are found within the main watch page structure
        if (element.closest('ytd-watch-flexy')) {
          element.style.setProperty('display', 'none', 'important');
        }
      });
    });
  }
}

// Function to show search results on watch pages (reverts hideSearchResultsOnWatchPages)
function showSearchResultsOnWatchPages() {
  if (window.location.pathname.includes('/watch')) {
    const searchSelectors = [
      'ytd-item-section-renderer[has-header]',
      'ytd-search-header-renderer',
      'ytd-search-sub-menu-renderer'
    ];
    searchSelectors.forEach(selector => {
      document.querySelectorAll(selector).forEach(element => {
        if (element.closest('ytd-watch-flexy')) {
          element.style.display = ''; // Revert display
        }
      });
    });
  }
}

// Intercept link clicks to prevent new tabs
document.addEventListener('click', (e) => {
  if (!settings.newTabBlockerEnabled) return;

  const link = e.target.closest('a');
  if (link && link.href?.includes('youtube.com')) {
    // Prevent middle-click and ctrl+click
    if (e.button === 1 || (e.button === 0 && (e.ctrlKey || e.metaKey))) {
      e.preventDefault();
      window.location.href = link.href;
    }
  }
});

// Intercept window.open calls
const originalWindowOpen = window.open;
window.open = function(url, ...args) {
  if (settings.newTabBlockerEnabled && url?.includes('youtube.com')) {
    window.location.href = url;
    return null;
  }
  return originalWindowOpen.call(this, url, ...args);
};

// Create a MutationObserver to watch for dynamically added content
const observer = new MutationObserver((mutations) => {
  if (settings.youtubeBlockerEnabled) { // Guard execution
    hideRecommendations();
    // expandVideoPlayer() call removed
  }
});

// Start observing the document with the configured parameters
observer.observe(document.body, {
  childList: true,
  subtree: true
});

// Removed ensureBlockerClass function as applySettings handles the class.

function onYouTubeNavigation() {
  applySettings(); // Re-apply settings on navigation
  // setTimeout for hideSearchResultsOnWatchPages is removed; applySettings handles it.
}

// Removed expandVideoPlayer function. All its styling should be in youtube-blocker.css
// and controlled by the .ytd-fyp-blocker-enabled class.

// NEW function to clear inline styles potentially set by old versions of expandVideoPlayer
function clearLegacyInlineStyles() {
  const elementsAndProperties = [
    { selector: 'ytd-watch-flexy #primary', properties: ['max-width', 'width', 'flex', 'margin-right'] },
    { selector: 'ytd-watch-flexy #secondary', properties: ['display'] },
    { selector: 'ytd-watch-flexy[flex-layout] #columns', properties: ['display', 'flex-direction', 'align-items'] },
    { selector: '#masthead-container', properties: ['position', 'left', 'width', 'margin-left', 'padding-left', 'padding-right', 'box-sizing'] },
    { selector: 'ytd-masthead', properties: ['width', 'box-sizing'] },
    { selector: 'ytd-masthead #container', properties: ['display', 'align-items', 'width', 'padding', 'box-sizing'] },
    { selector: 'ytd-masthead #container > #start', properties: ['flex'] },
    { selector: 'ytd-masthead #container > #center', properties: ['flex', 'min-width', 'margin'] },
    { selector: 'ytd-masthead #container > #center ytd-searchbox', properties: ['width', 'display', 'flex'] },
    { selector: 'ytd-masthead #container > #end', properties: ['flex'] }
  ];

  elementsAndProperties.forEach(item => {
    document.querySelectorAll(item.selector).forEach(element => {
      item.properties.forEach(prop => {
        element.style.removeProperty(prop);
      });
    });
  });
}


// Initial run:
// We wait for settings to be loaded before applying anything.
// applySettings() is called within the storage.get callback.

// Listen for YouTube SPA navigation events to re-apply settings
window.addEventListener('yt-navigate-finish', onYouTubeNavigation);
window.addEventListener('yt-page-data-updated', onYouTubeNavigation);