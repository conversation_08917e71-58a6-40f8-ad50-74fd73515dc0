// Browser compatibility
const browserAPI = typeof browser !== 'undefined' ? browser : chrome;

// Configuration
let settings = {
  newTabBlockerEnabled: false,
  youtubeBlockerEnabled: false
};

// Load settings from storage
browserAPI.storage.sync.get(['newTabBlockerEnabled', 'youtubeBlockerEnabled'], (result) => {
  if (result.newTabBlockerEnabled !== undefined) {
    settings.newTabBlockerEnabled = result.newTabBlockerEnabled;
  }
  if (result.youtubeBlockerEnabled !== undefined) {
    settings.youtubeBlockerEnabled = result.youtubeBlockerEnabled;
  }
  applyYouTubeBlocker();
});

// Listen for setting changes from storage
browserAPI.storage.onChanged.addListener(function(changes) {
  if (changes.newTabBlockerEnabled) {
    settings.newTabBlockerEnabled = changes.newTabBlockerEnabled.newValue;
  }
  if (changes.youtubeBlockerEnabled) {
    settings.youtubeBlockerEnabled = changes.youtubeBlockerEnabled.newValue;
    applyYouTubeBlocker();
  }
});

// Simple function to apply/remove YouTube blocker class
function applyYouTubeBlocker() {
  if (settings.youtubeBlockerEnabled) {
    document.documentElement.classList.add('ytd-fyp-blocker-enabled');
  } else {
    document.documentElement.classList.remove('ytd-fyp-blocker-enabled');
  }
}

// Listen for YouTube navigation events to reapply blocker
function onYouTubeNavigation() {
  applyYouTubeBlocker();
}

window.addEventListener('yt-navigate-finish', onYouTubeNavigation);
window.addEventListener('yt-page-data-updated', onYouTubeNavigation);

// ===== NEW TAB BLOCKER FUNCTIONALITY =====
// Intercept link clicks to prevent new tabs
document.addEventListener('click', (e) => {
  if (!settings.newTabBlockerEnabled) return;

  const link = e.target.closest('a');
  if (link && link.href?.includes('youtube.com')) {
    // Prevent middle-click and ctrl+click
    if (e.button === 1 || (e.button === 0 && (e.ctrlKey || e.metaKey))) {
      e.preventDefault();
      window.location.href = link.href;
    }
  }
});

// Intercept window.open calls
const originalWindowOpen = window.open;
window.open = function(url, ...args) {
  if (settings.newTabBlockerEnabled && url?.includes('youtube.com')) {
    window.location.href = url;
    return null;
  }
  return originalWindowOpen.call(this, url, ...args);
};