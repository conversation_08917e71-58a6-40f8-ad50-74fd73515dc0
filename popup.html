<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Flow</title>
  <style>
    body {
      width: 270px;
      background-color: #121212;
      color: #e0e0e0;
      font-family: Arial, sans-serif;
      padding: 10px 12px 12px 12px;
      margin: 0;
    }
    
    .container {
      display: flex;
      flex-direction: column;
      gap: 11px;
    }
    
    .option {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-left: 0;
    }
    
    .checkbox-container {
      position: relative;
      width: 20px;
      height: 20px;
      border: 1.5px solid #444;
      background-color: #1e1e1e;
      cursor: pointer;
      margin-right: 0;
    }
    
    .checkbox-container input {
      opacity: 0;
      position: absolute;
      cursor: pointer;
      width: 100%;
      height: 100%;
      margin: 0;
    }
    
    .checkmark {
      position: absolute;
      top: 0;
      left: 0;
      height: 20px;
      width: 20px;
      background-color: transparent;
    }
    
    .checkbox-container input:checked ~ .checkmark {
      background-color: #1e1e1e;
    }
    
    .checkmark:after {
      content: "";
      position: absolute;
      display: none;
    }
    
    .checkbox-container input:checked ~ .checkmark:after {
      display: block;
      left: 6px;
      top: 3px;
      width: 5px;
      height: 11px;
      border: solid white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
    }
    
    .option-label {
      font-size: 15px;
      font-weight: 500;
    }
    
    .title {
      font-size: 18px;
      margin-bottom: 10px;
      color: #fff;
      border-bottom: 1.5px solid #444;
      padding-bottom: 7px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .title-gif {
      width: 24px;
      height: 24px;
    }
    
    .blue-line {
      height: 2px;
      background-color: #3b82f6;
      margin-top: 10px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="title">
      <span>Flow</span>
      <img src="images/cockatiel.gif" alt="gif" class="title-gif">
    </div>
    
    <div class="option">
      <label class="checkbox-container">
        <input type="checkbox" id="youtube-blocker">
        <span class="checkmark"></span>
      </label>
      <span class="option-label">Block FYP</span>
    </div>
    
    <div class="option">
      <label class="checkbox-container">
        <input type="checkbox" id="new-tab-blocker">
        <span class="checkmark"></span>
      </label>
      <span class="option-label">Block New Tabs</span>
    </div>
    
    <div class="blue-line"></div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
