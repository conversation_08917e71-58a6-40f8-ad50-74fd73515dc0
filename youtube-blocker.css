/* YouTube FYP Blocker CSS */
/* Hide video recommendations on homepage and sidebar */
.ytd-fyp-blocker-enabled ytd-rich-grid-renderer,
.ytd-fyp-blocker-enabled ytd-watch-next-secondary-results-renderer,
.ytd-fyp-blocker-enabled ytd-shelf-renderer,
.ytd-fyp-blocker-enabled ytd-horizontal-card-list-renderer,
.ytd-fyp-blocker-enabled ytd-rich-section-renderer {
  display: none !important;
}

/* Hide channel recommendations */
.ytd-fyp-blocker-enabled ytd-guide-section-renderer:not(:first-child),
.ytd-fyp-blocker-enabled ytd-mini-guide-renderer {
  display: none !important;
}

/* Hide notifications */
.ytd-fyp-blocker-enabled ytd-notification-topbar-button-renderer {
  display: none !important;
}

/* Keep search functionality visible */
.ytd-fyp-blocker-enabled ytd-searchbox,
.ytd-fyp-blocker-enabled ytd-search {
  display: block !important;
}

/* Ensure search page content remains visible */
.ytd-fyp-blocker-enabled ytd-search-results,
.ytd-fyp-blocker-enabled ytd-section-list-renderer[is-search-results] {
  display: block !important;
}

/* Keep header visible for navigation */
.ytd-fyp-blocker-enabled ytd-masthead {
  display: flex !important;
}

/* Keep subscriptions visible */
.ytd-fyp-blocker-enabled ytd-guide-section-renderer:first-child {
  display: block !important;
}

/* Keep video player visible on watch pages */
.ytd-fyp-blocker-enabled ytd-watch-flexy #primary {
  display: block !important;
}

.ytd-fyp-blocker-enabled ytd-watch-flexy #secondary {
  /* Make the sidebar panel visible */
  display: block !important;
}

.ytd-fyp-blocker-enabled ytd-watch-flexy[flex-layout] #columns {
  /* This rule should already exist and define the flex layout for primary/secondary */
  display: flex !important;
  flex-direction: row !important;
  align-items: stretch !important;
}

.ytd-fyp-blocker-enabled ytd-watch-flexy[flex-layout] #primary {
  /* If #primary still expands, we might need to set flex: initial; or similar */
}

/* Hide specific recommendation items within the now-visible sidebar */
.ytd-fyp-blocker-enabled ytd-watch-flexy #secondary ytd-compact-video-renderer,
.ytd-fyp-blocker-enabled ytd-watch-flexy #secondary ytd-compact-playlist-renderer,
.ytd-fyp-blocker-enabled ytd-watch-flexy #secondary ytd-compact-radio-renderer,
.ytd-fyp-blocker-enabled ytd-watch-flexy #secondary ytd-compact-movie-renderer,
.ytd-fyp-blocker-enabled ytd-watch-flexy #secondary ytd-merchandise-shelf-renderer,
.ytd-fyp-blocker-enabled ytd-watch-flexy #secondary ytd-video-with-context-renderer,
.ytd-fyp-blocker-enabled ytd-watch-flexy #secondary ytd-promoted-sparkles-web-renderer,
.ytd-fyp-blocker-enabled ytd-watch-flexy #secondary ytd-engagement-panel-section-list-renderer, /* For live chat replay or other panels */
.ytd-fyp-blocker-enabled ytd-watch-flexy #secondary ytd-donation-shelf-renderer {
  display: none !important;
}

/* The rule below should already hide the main list of "Up next" videos,
   it's a general rule but good to ensure it's effective for the sidebar too. */
.ytd-fyp-blocker-enabled ytd-watch-next-secondary-results-renderer {
  display: none !important;
}

/* Optionally, hide only the right sidebar recommendations, not the whole sidebar */
/* This rule might be redundant if the more specific item rules above cover everything,
   but can serve as a fallback for the #related container. */
.ytd-fyp-blocker-enabled #related {
  display: none !important;
}

/* Hide search results that persist on watch pages */
.ytd-fyp-blocker-enabled ytd-watch-flexy ytd-item-section-renderer[page-subtype="search"],
.ytd-fyp-blocker-enabled ytd-watch-flexy ytd-item-section-renderer[has-header],
.ytd-fyp-blocker-enabled ytd-watch-flexy ytd-search-pyv-renderer,
.ytd-fyp-blocker-enabled ytd-watch-flexy ytd-search-header-renderer,
.ytd-fyp-blocker-enabled ytd-watch-flexy ytd-search-sub-menu-renderer {
  display: none !important;
}

/* Hide the search filter chips that might appear on watch pages */
.ytd-fyp-blocker-enabled ytd-watch-flexy ytd-feed-filter-chip-bar-renderer {
  display: none !important;
}

/* Additional selectors to hide persistent search results on watch pages */
.ytd-fyp-blocker-enabled ytd-watch-flexy ytd-two-column-search-results-renderer,
.ytd-fyp-blocker-enabled ytd-watch-flexy ytd-section-list-renderer[page-subtype="search"],
.ytd-fyp-blocker-enabled ytd-watch-flexy ytd-secondary-search-container-renderer,
.ytd-fyp-blocker-enabled ytd-watch-flexy ytd-item-section-renderer ytd-video-renderer,
.ytd-fyp-blocker-enabled ytd-watch-flexy #secondary[has-search-results="true"],
.ytd-fyp-blocker-enabled ytd-watch-flexy #secondary ytd-item-section-renderer ytd-video-renderer,
.ytd-fyp-blocker-enabled ytd-watch-flexy #secondary ytd-continuation-item-renderer {
  display: none !important;
}

/* Target the entire secondary column when it contains search results */
.ytd-fyp-blocker-enabled ytd-watch-flexy[page-subtype="search"] #secondary {
  display: none !important;
}
